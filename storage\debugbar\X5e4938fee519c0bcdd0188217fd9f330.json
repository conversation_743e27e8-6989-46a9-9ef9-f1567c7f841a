{"__meta": {"id": "X5e4938fee519c0bcdd0188217fd9f330", "datetime": "2025-07-22 22:23:09", "utime": 1753222989.208859, "method": "GET", "uri": "/case-study/lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months", "ip": "127.0.0.1"}, "php": {"version": "8.3.17", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.040869, "end": 1753222989.208878, "duration": 1.1680090427398682, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": **********.040869, "relative_start": 0, "end": **********.76401, "relative_end": **********.76401, "duration": 0.7231409549713135, "duration_str": "723ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.764023, "relative_start": 0.7231540679931641, "end": 1753222989.20888, "relative_end": 1.9073486328125e-06, "duration": 0.4448568820953369, "duration_str": "445ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43663296, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 6, "templates": [{"name": "theme.goalconversion::views.case-study", "param_count": null, "params": [], "start": **********.880963, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/views/case-study.blade.phptheme.goalconversion::views.case-study", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fviews%2Fcase-study.blade.php&line=1", "ajax": false, "filename": "case-study.blade.php", "line": "?"}}, {"name": "theme.goalconversion::layouts.default", "param_count": null, "params": [], "start": **********.886595, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/layouts/default.blade.phptheme.goalconversion::layouts.default", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.header", "param_count": null, "params": [], "start": **********.887254, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/header.blade.phptheme.goalconversion::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "packages/theme::partials.header", "param_count": null, "params": [], "start": **********.889881, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/header.blade.phppackages/theme::partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "theme.goalconversion::partials.footer", "param_count": null, "params": [], "start": **********.903372, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform\\themes/goalconversion/partials/footer.blade.phptheme.goalconversion::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fthemes%2Fgoalconversion%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}, {"name": "packages/theme::partials.footer", "param_count": null, "params": [], "start": 1753222989.202224, "type": "blade", "hash": "bladeD:\\laragon\\www\\goalconversion\\platform/packages/theme/resources/views/partials/footer.blade.phppackages/theme::partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fpackages%2Ftheme%2Fresources%2Fviews%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "route": {"uri": "GET {prefix}/{slug?}", "middleware": "web, core", "controller": "Shaqi\\Theme\\Http\\Controllers\\PublicController@getViewWithPrefix", "namespace": null, "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Ftheme%2Fsrc%2FHttp%2FControllers%2FPublicController.php&line=114\" onclick=\"\">vendor/shaqi/theme/src/Http/Controllers/PublicController.php:114-117</a>"}, "queries": {"nb_statements": 7, "nb_visible_statements": 7, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00333, "accumulated_duration_str": "3.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `slugs` where (`key` = 'lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months' and `prefix` = 'case-study') limit 1", "type": "query", "params": [], "bindings": ["lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months", "case-study"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/shaqi/slug/src/SlugHelper.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\slug\\src\\SlugHelper.php", "line": 182}, {"index": 19, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 50}, {"index": 20, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.821789, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 0, "width_percent": 16.216}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 339}], "start": **********.831662, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 16.216, "width_percent": 12.613}, {"sql": "select * from `case_studies` where (`id` = 1 and `status` = 'published') limit 1", "type": "query", "params": [], "bindings": [1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/case-studies/src/Services/CaseStudyService.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Services\\CaseStudyService.php", "line": 47}, {"index": 18, "namespace": null, "name": "platform/plugins/case-studies/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php", "line": 75}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}], "start": **********.836094, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 28.829, "width_percent": 16.817}, {"sql": "select `case_study_categories`.*, `case_study_category_pivot`.`case_study_id` as `pivot_case_study_id`, `case_study_category_pivot`.`category_id` as `pivot_category_id` from `case_study_categories` inner join `case_study_category_pivot` on `case_study_categories`.`id` = `case_study_category_pivot`.`category_id` where `case_study_category_pivot`.`case_study_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/plugins/case-studies/src/Services/CaseStudyService.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Services\\CaseStudyService.php", "line": 47}, {"index": 22, "namespace": null, "name": "platform/plugins/case-studies/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php", "line": 75}, {"index": 26, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 28, "namespace": null, "name": "vendor/shaqi/theme/src/Http/Controllers/PublicController.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\theme\\src\\Http\\Controllers\\PublicController.php", "line": 116}], "start": **********.841944, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 19, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 45.646, "width_percent": 15.315}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Shaqi\\\\CaseStudies\\\\Models\\\\CaseStudyCategory'", "type": "query", "params": [], "bindings": ["Shaqi\\CaseStudies\\Models\\CaseStudyCategory"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 25, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/plugins/case-studies/src/Services/CaseStudyService.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Services\\CaseStudyService.php", "line": 47}, {"index": 28, "namespace": null, "name": "platform/plugins/case-studies/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php", "line": 75}, {"index": 32, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.8484151, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 60.961, "width_percent": 13.213}, {"sql": "select `id`, `key`, `reference_type`, `reference_id`, `prefix` from `slugs` where `slugs`.`reference_id` in (1) and `slugs`.`reference_type` = 'Shaqi\\\\CaseStudies\\\\Models\\\\CaseStudy'", "type": "query", "params": [], "bindings": ["Shaqi\\CaseStudies\\Models\\CaseStudy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 23, "namespace": null, "name": "platform/plugins/case-studies/src/Services/CaseStudyService.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Services\\CaseStudyService.php", "line": 47}, {"index": 24, "namespace": null, "name": "platform/plugins/case-studies/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php", "line": 75}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.850274, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 74.174, "width_percent": 9.61}, {"sql": "select `reference_id`, `reference_type`, `meta_key`, `meta_value` from `meta_boxes` where `meta_boxes`.`reference_id` in (1) and `meta_boxes`.`reference_type` = 'Shaqi\\\\CaseStudies\\\\Models\\\\CaseStudy'", "type": "query", "params": [], "bindings": ["Shaqi\\CaseStudies\\Models\\CaseStudy"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 24, "namespace": null, "name": "vendor/shaqi/seo-helper/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\seo-helper\\src\\Providers\\HookServiceProvider.php", "line": 87}, {"index": 28, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 46}, {"index": 30, "namespace": null, "name": "platform/plugins/case-studies/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\plugins\\case-studies\\src\\Providers\\HookServiceProvider.php", "line": 75}, {"index": 34, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\goalconversion\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.873879, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "vendor/shaqi/platform/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\goalconversion\\vendor\\shaqi\\platform\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "goalconversion", "explain": null, "start_percent": 83.784, "width_percent": 16.216}]}, "models": {"data": {"Shaqi\\Slug\\Models\\Slug": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fslug%2Fsrc%2FModels%2FSlug.php&line=1", "ajax": false, "filename": "Slug.php", "line": "?"}}, "Shaqi\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Shaqi\\CaseStudies\\Models\\CaseStudy": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Fcase-studies%2Fsrc%2FModels%2FCaseStudy.php&line=1", "ajax": false, "filename": "CaseStudy.php", "line": "?"}}, "Shaqi\\CaseStudies\\Models\\CaseStudyCategory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fplatform%2Fplugins%2Fcase-studies%2Fsrc%2FModels%2FCaseStudyCategory.php&line=1", "ajax": false, "filename": "CaseStudyCategory.php", "line": "?"}}, "Shaqi\\Base\\Models\\MetaBox": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fgoalconversion%2Fvendor%2Fshaqi%2Fplatform%2Fbase%2Fsrc%2FModels%2FMetaBox.php&line=1", "ajax": false, "filename": "MetaBox.php", "line": "?"}}}, "count": 8, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "K4lEHChQQ1Erb1G7Fdf94xewlR9aRAs2o5MmelU8", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"https://goalconversion.gc/case-study/lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "viewed_case_study": "array:1 [\n  1 => 1753209877\n]"}, "request": {"path_info": "/case-study/lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months", "status_code": "<pre class=sf-dump id=sf-dump-1557673598 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1557673598\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1537280549 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1537280549\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1947213649 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1947213649\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1338101924 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">goalconversion.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">https://goalconversion.gc/gc/lgn/case-studies/case-studies/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1370 characters\">_ga=GA1.1.996387623.1750873576; cookie_for_consent=1; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6ImJqeUJpUklyZGNINDVwa09NNnJiTGc9PSIsInZhbHVlIjoiZ1picmMxRWU5V2xQb1haQVdaSEtsSVo1dGtFbnAzK3E4T0N6VVU1TThlck9Pb01hODBlQzVCeld0N2UrS29uMFlpSGdqZyttdzQ0K20rcE55WHhCbVZhb05BaXBSVHNEY1ZMa05kYThpbS9PUURhUWZuVkpsT1JzZW1DZ2ViR1NxWVg5aVZDRktqTzF6NkpqSHVKRmtwNWlhdHlNQVp3aWJmWU8rZnBCVURubVVsMjcxTjFvV2VZd01ibTlZc2xKcWRVMHJWNHdwNG1iaThsR1JLWTlEMDRkRGJUQjVlTlZPanpsL1FPMHlMUT0iLCJtYWMiOiIzZTBiNWRhYjZiNmFjOWZlZTVhNDVlNDdkNjRlZGI2NTA0MTZhYjdhZmFjOWJmYTBjYjMwNzA1YmQyZGE3YmUxIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9hQmxsSC9EU3pTSFFETUFxWHAxUWc9PSIsInZhbHVlIjoiOEFNWUxHVWkwbThzVXZxVVJRaW91WnJvZUxibExPajVKYi9SRDRKaWozalN4K1V1MnhhM0Q2RklQblplN1cyTmd2UFN6WUJqcFlOSzErR3l0eUd5dDNLR1FURFNCTWdtOGRrVDJKWUlDSHhMdWY4Y0lxeTNnTUozVGtRS1dvRG0iLCJtYWMiOiJkZTNmYTE2MDg1N2Q2ZGJkMWU0NDBiYjcwMmRiZTZhZGZmNTc2MzhlNmE5MGE4NmZlMzgxZDhkN2EzY2E0N2IwIiwidGFnIjoiIn0%3D; shaqi_session=eyJpdiI6IlVXbTRhUG1uZWsrckh4Mm9jdHl3RVE9PSIsInZhbHVlIjoiVklNb3Z0UlFGaDdsZGFyWjlzRjNjUlUybE9ldTNQU1dkS29BSGdEWE1VcHhPUVh3NzNUZTd2S2RKdU5vNk14N1NFV2s5UTVVSVFBU0wycmdRL2l5SEl2WFkyY09TbEFONXFyS3JKRmQvb0tBSVhDdllrNUtlZjJseFJ6eDRrY1EiLCJtYWMiOiIzMWRjZDQ5MDI0ZTQ5MzMyNGRiMGM2ODM2MTZhOTgzMGU3NDIyNWZjMTQ1MDFiOTUyMWNjMjczNWNjOWUwMGM0IiwidGFnIjoiIn0%3D; _ga_FSKD72EXSB=GS2.1.s1753216466$o52$g1$t1753221960$j56$l0$h0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1338101924\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-194772464 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|93AZBoOuq93wZdZjYcXin3OIxaW8YfC1pd64jsSLusTXT7oOXJKvnP4Pzefq|$2y$12$4xmFIOy4V2zK3g4n9iqTEuh93mpXc3HCy/DDmxWzDBRr3EJiAeNFG</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K4lEHChQQ1Erb1G7Fdf94xewlR9aRAs2o5MmelU8</span>\"\n  \"<span class=sf-dump-key>shaqi_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HmnXHw9AG7u0j7SaHAilQYCOuscbutxfNnn4KfPj</span>\"\n  \"<span class=sf-dump-key>_ga_FSKD72EXSB</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-194772464\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1777617547 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 22 Jul 2025 22:23:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cms-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">7.5.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization-at</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>activated-license</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">Yes</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"436 characters\">XSRF-TOKEN=eyJpdiI6IkU1NmYrZDJ6di9LSHNmWXZRQm5ONmc9PSIsInZhbHVlIjoiSElBbTNsZFVJUFpUUjdiU3R3OHdtSUpwVHhTcEtHSFN0K1E1OXBmWkQ2Y2hwYUF0Q3ppU1BSTk0wYWRBbGtOQTg4WGZ2UzJ3QkRSd1AwMDNDN1pucUMwMTRKeUlFMm5QUHJBQmREV0N0VjBlTDFpTTlWNi8zN0NxdWU1RXlFVHYiLCJtYWMiOiJmNWNiNDYzYmUxNzQ0YmZmZTlhODI3OTdiODhlYmY1YzI5YTRkOGQ4N2RkYmM4NmQ0NjJiMDA1ZTY3Y2M2MDRkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 00:23:09 GMT; Max-Age=7200; path=/; secure; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"449 characters\">shaqi_session=eyJpdiI6InRSRGMvVHNiOWNJK054S0tCR3F0b3c9PSIsInZhbHVlIjoicmRJK1JhQU9meUY1VHpjckx6ODFQOXk2MkZycUhXdFRJZkY0TVRWTFN1R2txU2NZd2xBTzdJc2hjMWhub1o2cDE1SU5TNFpQWGtzcmxMNG56NGxrTFBnaTBNWVRKditrQ25uM2RVNVFscUd4Sm1vQ2E5cFp2TEhZb3p2dURyaVUiLCJtYWMiOiJlZWRjMjlkM2E2Nzk4NGZjMTM1NmFjNDQwNzRiN2NhNTk0NDdjNTMwNjEyZTM5MzQ5ZjhlOTE2ZmYyZjEzNzlkIiwidGFnIjoiIn0%3D; expires=Wed, 23 Jul 2025 00:23:09 GMT; Max-Age=7200; path=/; secure; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"408 characters\">XSRF-TOKEN=eyJpdiI6IkU1NmYrZDJ6di9LSHNmWXZRQm5ONmc9PSIsInZhbHVlIjoiSElBbTNsZFVJUFpUUjdiU3R3OHdtSUpwVHhTcEtHSFN0K1E1OXBmWkQ2Y2hwYUF0Q3ppU1BSTk0wYWRBbGtOQTg4WGZ2UzJ3QkRSd1AwMDNDN1pucUMwMTRKeUlFMm5QUHJBQmREV0N0VjBlTDFpTTlWNi8zN0NxdWU1RXlFVHYiLCJtYWMiOiJmNWNiNDYzYmUxNzQ0YmZmZTlhODI3OTdiODhlYmY1YzI5YTRkOGQ4N2RkYmM4NmQ0NjJiMDA1ZTY3Y2M2MDRkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 00:23:09 GMT; path=/; secure</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"421 characters\">shaqi_session=eyJpdiI6InRSRGMvVHNiOWNJK054S0tCR3F0b3c9PSIsInZhbHVlIjoicmRJK1JhQU9meUY1VHpjckx6ODFQOXk2MkZycUhXdFRJZkY0TVRWTFN1R2txU2NZd2xBTzdJc2hjMWhub1o2cDE1SU5TNFpQWGtzcmxMNG56NGxrTFBnaTBNWVRKditrQ25uM2RVNVFscUd4Sm1vQ2E5cFp2TEhZb3p2dURyaVUiLCJtYWMiOiJlZWRjMjlkM2E2Nzk4NGZjMTM1NmFjNDQwNzRiN2NhNTk0NDdjNTMwNjEyZTM5MzQ5ZjhlOTE2ZmYyZjEzNzlkIiwidGFnIjoiIn0%3D; expires=Wed, 23-Jul-2025 00:23:09 GMT; path=/; secure; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1777617547\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">K4lEHChQQ1Erb1G7Fdf94xewlR9aRAs2o5MmelU8</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"104 characters\">https://goalconversion.gc/case-study/lorem-ipsum-achieved-an-890-increase-in-conversions-in-eight-months</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>viewed_case_study</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => <span class=sf-dump-num>1753209877</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}